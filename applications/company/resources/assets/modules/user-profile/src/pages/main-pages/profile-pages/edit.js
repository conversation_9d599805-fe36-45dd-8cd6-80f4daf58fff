'use strict';

const $ = require("jquery");
const autosize = require('autosize');
const Inputmask = require("inputmask");
const Uppy = require('@uppy/core');
const ThumbnailGenerator = require('@uppy/thumbnail-generator');

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onClick, onClickWatcher} = require("@ca-package/dom");

const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
require('@cac-js/handlebars-helpers/if_eq');
const FormValidator = require("@cas-validator-js");

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));

const Tooltip = require('@ca-submodule/tooltip');

const current_image_tpl = require('@cam-user-profile-tpl/pages/main-pages/profile/current_image.hbs');
const edit_tpl = require('@cam-user-profile-tpl/pages/main-pages/profile/edit.hbs');
const phone_row_tpl = require('@cam-user-profile-tpl/pages/main-pages/profile/phone_row.hbs');

const PhoneDescriptions = {
    Main: 'Main',
    Cell: 'Cell',
    Home: 'Home',
    Work: 'Work',
    Other: 'Other'
};

class Edit extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            phone_rows: new Map,
            phone_idx: 0,
            primary_phone_id: null,
            uppy_file_id: null,
            edited: false,
            has_image: false
        });
    };

    /**
     * Clear page
     */
    clear() {
        this.elem.contact_info.empty();
    };

    /**
     * Populate data into profile template
     *
     * @param {object} data
     */
    populate(data) {
        this.state.validator.getInputElem('first_name').val(data.first_name);
        this.state.validator.getInputElem('last_name').val(data.last_name);
        this.state.validator.getInputElem('email').val(data.email);
        this.state.validator.getInputElem('bio').val(data.bio).trigger('change');

        if (data.image_file_id !== null) {
            this.addImage(data.image_media_urls.profile_thumbnail, true);
        }

        for (let phone of data.phones) {
            this.addPhoneRow(phone);
        }

        // this.elem.email_notification_task_due.prop('checked', data.settings.email_notification_task_due).trigger('change');

        this.elem.email_notification_task_assigned.prop('checked', data.settings.email_notification_task_assigned).trigger('change');
        this.elem.app_notification_task_assigned.prop('checked', data.settings.app_notification_task_assigned).trigger('change');

        this.elem.email_notification_lead_assigned.prop('checked', data.settings.email_notification_lead_assigned).trigger('change');
        this.elem.app_notification_lead_assigned.prop('checked', data.settings.app_notification_lead_assigned).trigger('change');

        this.elem.email_notification_project_assigned.prop('checked', data.settings.email_notification_project_assigned).trigger('change');
        this.elem.app_notification_project_assigned.prop('checked', data.settings.app_notification_project_assigned).trigger('change');

        this.elem.email_notification_appointment_scheduled.prop('checked', data.settings.email_notification_appointment_scheduled).trigger('change');
        this.elem.app_notification_appointment_scheduled.prop('checked', data.settings.app_notification_appointment_scheduled).trigger('change');

        this.elem.email_notification_bid_viewed.prop('checked', data.settings.email_notification_bid_viewed).trigger('change');
        this.elem.app_notification_bid_viewed.prop('checked', data.settings.app_notification_bid_viewed).trigger('change');

        this.elem.email_notification_bid_accepted.prop('checked', data.settings.email_notification_bid_accepted).trigger('change');
        this.elem.app_notification_bid_accepted.prop('checked', data.settings.app_notification_bid_accepted).trigger('change');

        this.elem.email_notification_bid_rejected.prop('checked', data.settings.email_notification_bid_rejected).trigger('change');
        this.elem.app_notification_bid_rejected.prop('checked', data.settings.app_notification_bid_rejected).trigger('change');

        this.state.parent.setParentModuleBorder();
    };

    /**
     * Adds thumbnail image and sets click event for the delete image
     *
     * @param {string} source
     * @param {boolean} from_url
     */
    addImage(source, from_url = false) {
        let image = new Image();
        if (from_url) {
            image.src = `${source}?rand=${Math.random() * 99999999}`;
        } else {
            image.src = source;
        }
        let current_image = $(current_image_tpl());
        this.elem.thumbnail_image = findChild(current_image, jsSelector('thumbnail'));
        this.elem.delete_image = findChild(current_image, jsSelector('delete_image'));
        this.elem.delete_image.fxEvent('click', () => {
            this.deleteImage();
        });
        this.elem.thumbnail_image.append(image);
        this.elem.current_image.append(current_image);
        this.state.has_image = true;
    };

    /**
     * Remove uppy file
     */
    removeUppyFile() {
        if (this.state.uppy_file_id !== null) {
            this.state.uppy.removeFile(this.state.uppy_file_id);
        }
    };

    /**
     * Removes the user-profile image thumbnail
     */
    deleteImage() {
        this.removeUppyFile();
        this.elem.file.val('');
        this.elem.current_image.empty();
        this.state.has_image = false;
    };

    /**
     * Fetch data from server
     *
     * @returns {Promise<void>}
     */
    async fetchData() {
        try {

            let {data: entity} = await Api.Resources.Users()
                .fields(['first_name', 'last_name', 'email', 'bio', 'image_file_id'])
                .relations({
                    phones: {
                        'fields': ['id', 'number', 'description', 'is_primary']
                    },
                    image_media_urls: {},
                    settings: {}
                })
                .retrieve('current');
            this.populate(entity);
        } catch (e) {
            let message = createErrorMessage('Unable to fetch user info, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
            console.log(e);
        }
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    async refresh(request) {
        if (request.query.update === 'true') {
            this.clear();
            await this.fetchData();
        }
    };

    /**
     * Add phone row
     *
     * @param {object} data
     */
    addPhoneRow(data) {
        if (data.is_primary) {
            this.state.primary_phone_id = this.state.phone_idx;
        }
        data.descriptions = PhoneDescriptions;
        data.index = this.state.phone_idx++;
        let elem = {
            root: $(phone_row_tpl(data)),
            input: {},
            field: {}
        };
        for (let name of ['description', 'number', 'primary']) {
            elem.input[name] = elem.root.fxFind(name);
        }

        let fields = {
            description: {
                required: true,
                maxlength: 20
            },
            number: {
                required: true,
                pattern: '^\\(\\d{3}\\)\\s\\d{3}-\\d{4}',
                patternMessage: 'Number must be formatted as (XXX) XXX-XXXX.'
            }
        };

        for (let name in fields) {
            fields[name].requiredMessage = 'Required';
            elem.field[name] = elem.input[name].parsley(fields[name]);
        }

        Inputmask({
            "mask": "(*************"
        }).mask(elem.input.number);

        this.state.phone_rows.set(data.index, {
            data,
            elem
        });
        this.elem.phone_table.append(elem.root);
    };

    /**
     * Delete phone row
     *
     * @param {number} id
     */
    deletePhoneRow(id) {
        let phone = this.state.phone_rows.get(id);
        phone.elem.root.remove();
        this.state.phone_rows.delete(id);

        if (this.state.primary_phone_id === id && this.state.phone_rows.size > 0) {
            this.handlePrimarySwitch(Array.from(this.state.phone_rows.keys())[0], false);
        }
    };

    /**
     * Handle primary flag to ensure on per user
     *
     * @param {number} id
     * @param {boolean} [uncheck_old=true]
     */
    handlePrimarySwitch(id, uncheck_old = true) {
        this.state.phone_rows.get(id).elem.input.primary.addClass('t-checked');
        this.state.phone_rows.get(id).elem.input.primary.attr('checked', true);
        if (uncheck_old) {
            this.state.phone_rows.get(this.state.primary_phone_id).elem.input.primary.removeClass('t-checked');
            this.state.phone_rows.get(this.state.primary_phone_id).elem.input.primary.attr('checked', false);

        }
        this.state.primary_phone_id = id;
    };

    /**
     * Clear form data and reset validator
     */
    resetForm() {
        this.state.validator.reset();
        this.state.validator.getInputElem('first_name').val('');
        this.state.validator.getInputElem('last_name').val('');
        this.state.validator.getInputElem('email').val('');
        this.state.validator.getInputElem('bio').val('');

        for (let [key, phone] of this.state.phone_rows.entries()) {
            this.deletePhoneRow(key);
        }

        this.deleteImage();
    };

    /**
     * Save the information entered into the edit details page form
     */
    save() {
        let data = {
            first_name: this.state.validator.getInputElem('first_name').val(),
            last_name: this.state.validator.getInputElem('last_name').val(),
            email: this.state.validator.getInputElem('email').val(),
            bio: this.state.validator.getInputElem('bio').val(),
            phones: []
        };

        if (this.elem.file[0].files[0] === undefined && !this.state.has_image) {
            data['image_file_id'] = null;
        }

        for (let [key, phone] of this.state.phone_rows.entries()) {
            data.phones.push({
                id: phone.data.id,
                number: phone.elem.input.number.val(),
                description: phone.elem.input.description.val(),
                is_primary: key === this.state.primary_phone_id
            });
        }

        data['settings'] = {
            // email_notification_task_due: this.elem.email_notification_task_due.is(':checked'),

            email_notification_task_assigned: this.elem.email_notification_task_assigned.is(':checked'),
            app_notification_task_assigned: this.elem.app_notification_task_assigned.is(':checked'),

            email_notification_lead_assigned: this.elem.email_notification_lead_assigned.is(':checked'),
            app_notification_lead_assigned: this.elem.app_notification_lead_assigned.is(':checked'),

            email_notification_project_assigned: this.elem.email_notification_project_assigned.is(':checked'),
            app_notification_project_assigned: this.elem.app_notification_project_assigned.is(':checked'),

            email_notification_appointment_scheduled: this.elem.email_notification_appointment_scheduled.is(':checked'),
            app_notification_appointment_scheduled: this.elem.app_notification_appointment_scheduled.is(':checked'),

            email_notification_bid_viewed: this.elem.email_notification_bid_viewed.is(':checked'),
            app_notification_bid_viewed: this.elem.app_notification_bid_viewed.is(':checked'),

            email_notification_bid_accepted: this.elem.email_notification_bid_accepted.is(':checked'),
            app_notification_bid_accepted: this.elem.app_notification_bid_accepted.is(':checked'),

            email_notification_bid_rejected: this.elem.email_notification_bid_rejected.is(':checked'),
            app_notification_bid_rejected: this.elem.app_notification_bid_rejected.is(':checked')
        };

        Api.Resources.Users().partialUpdate('current', data).then((entity, response) => {
            if (this.elem.file[0].files[0] !== undefined) {
                Api.Resources.Users().file(this.elem.file[0].files[0]).method(Api.Request.Method.PUT).custom(`current/image`).then(() => {
                    this.resetForm();
                    this.router.navigate('profile.details');
                    this.state.parent.emitEvent();
                });
            } else {
                this.resetForm();
                this.state.edited = true;
                this.router.navigate('profile.details');
                this.state.parent.emitEvent();
            }
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    break;
                default:
                    break;
            }
        });

    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            first_name: {
                required: true,
                maxlength: 50,
                maxlengthMessage: 'Invalid length - 50 chars. max'
            },
            last_name: {
                required: true,
                maxlength: 50,
                maxlengthMessage: 'Invalid length - 50 chars. max'
            },
            email: {
                required: true,
                type: 'email',
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            },
            bio: {
                maxlength: 1000,
                maxlengthMessage: 'Invalid length - 1000 chars. max'
            }
        })
            .on('submit', () => this.save());
    };

    /**
     * Triggering submit of the form to save data
     *
     * Used in parent module on edit button
     *
     */
    triggerSubmit() {
        this.elem.form.trigger('submit');
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.state.parent.setEditMode(true);
        this.state.parent.startEditMode();

        this.elem.loader.show();
        this.fetchData().then(() => this.elem.loader.hide());

        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.resetForm();
        this.state.parent.setEditMode(false);
        if (this.state.edited) {
            request.data.update = true;
            this.state.edited = false;
        }
        this.state.has_image = false;
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        Tooltip.initAll(root);

        this.elem.loader = this.elem.root.fxFind('loader');
        this.elem.form = findChild(root, jsSelector('form'));
        this.initForm();
        this.elem.upload_button = findChild(root, jsSelector('upload'));
        this.elem.file = findChild(root, jsSelector('file'));

        this.elem.current_image = findChild(root, jsSelector('current-image'));
        this.elem.image_error = findChild(root, jsSelector('image-error'));

        this.elem.row_add = findChild(root, jsSelector('row-add'));
        this.elem.phone_table = findChild(root, jsSelector('phone-table'));
        this.elem.phone_table.addClass('t-open');

        this.elem.textarea = this.state.validator.getInputElem('bio');

        // email notification switches
        // this.elem.email_notification_task_due = findChild(root, jsSelector('email-notification-task-due'));

        this.elem.email_notification_task_assigned = findChild(root, jsSelector('email-notification-task-assigned'));
        this.elem.app_notification_task_assigned = findChild(root, jsSelector('app-notification-task-assigned'));

        this.elem.email_notification_lead_assigned = findChild(root, jsSelector('email-notification-lead-assigned'));
        this.elem.app_notification_lead_assigned = findChild(root, jsSelector('app-notification-lead-assigned'));

        this.elem.email_notification_project_assigned = findChild(root, jsSelector('email-notification-project-assigned'));
        this.elem.app_notification_project_assigned = findChild(root, jsSelector('app-notification-project-assigned'));

        this.elem.email_notification_appointment_scheduled = findChild(root, jsSelector('email-notification-appointment-scheduled'));
        this.elem.app_notification_appointment_scheduled = findChild(root, jsSelector('app-notification-appointment-scheduled'));

        this.elem.email_notification_bid_viewed = findChild(root, jsSelector('email-notification-bid-viewed'));
        this.elem.app_notification_bid_viewed = findChild(root, jsSelector('app-notification-bid-viewed'));

        this.elem.email_notification_bid_accepted = findChild(root, jsSelector('email-notification-bid-accepted'));
        this.elem.app_notification_bid_accepted = findChild(root, jsSelector('app-notification-bid-accepted'));

        this.elem.email_notification_bid_rejected = findChild(root, jsSelector('email-notification-bid-rejected'));
        this.elem.app_notification_bid_rejected = findChild(root, jsSelector('app-notification-bid-rejected'));

        // FormInput.init(this.elem.email_notification_task_due);
        FormInput.init(this.elem.email_notification_appointment_scheduled);
        FormInput.init(this.elem.app_notification_appointment_scheduled);

        FormInput.init(this.elem.email_notification_task_assigned);
        FormInput.init(this.elem.app_notification_task_assigned);

        FormInput.init(this.elem.email_notification_lead_assigned);
        FormInput.init(this.elem.app_notification_lead_assigned);

        FormInput.init(this.elem.email_notification_project_assigned);
        FormInput.init(this.elem.app_notification_project_assigned);

        FormInput.init(this.elem.email_notification_bid_viewed);
        FormInput.init(this.elem.app_notification_bid_viewed);

        FormInput.init(this.elem.email_notification_bid_accepted);
        FormInput.init(this.elem.app_notification_bid_accepted);

        FormInput.init(this.elem.email_notification_bid_rejected);
        FormInput.init(this.elem.app_notification_bid_rejected);

        autosize(this.elem.textarea[0]);
        this.elem.textarea.fxEvent('change', () => {
            autosize.update(this.elem.textarea);
        });

        let that = this;
        onClickWatcher(this.elem.phone_table, jsSelector('action-delete'), function () {
            that.deletePhoneRow($(this).fxParents('phone').data('id'));
            if (that.state.phone_rows.size === 0) {
                that.addPhoneRow({
                    id: null,
                    phone_number: '',
                    is_primary: true
                });
            }
        }, true);

        onClick(this.elem.row_add, () => {
            this.addPhoneRow({
                id: null,
                phone_number: '',
                is_primary: false
            });
            return false;
        });

        onClickWatcher(this.elem.phone_table, jsSelector('primary'), function (e) {
            let phone_row_id = $(this).fxParents('phone').data('id');
            if (phone_row_id === that.state.primary_phone_id) {
                e.preventDefault();
                return false;
            }
            that.handlePrimarySwitch(phone_row_id);
        }, true);

        this.state.uppy = Uppy({
            id: 'profile-image-upload',
            autoProceed: false,
            restrictions: {
                allowedFileTypes: ['image/jpeg', 'image/png'],
                maxNumberOfFiles: 1,
                maxFileSize: 5242880
            }
        }).use(ThumbnailGenerator, {
            thumbnailWidth: 64,
            thumbnailHeight: 64,
            thumbnailType: 'image/jpeg',
            waitForThumbnailsBeforeUpload: false,
        }).on('file-added', (file) => {
            this.state.uppy_file_id = file.id;
        }).on('thumbnail:generated', (file, preview) => {
            this.addImage(preview);
        });

        onClick(this.elem.upload_button, () => {
            this.elem.file.trigger('click');
            return false;
        });

        this.elem.file.on('change', () => {
            this.elem.image_error.empty();
            let file = this.elem.file[0].files[0];
            this.removeUppyFile();

            try {
                this.elem.current_image.empty();
                this.state.uppy.addFile({
                    source: 'Local',
                    name: file.name,
                    type: file.type,
                    data: file
                });
            } catch (err) {
                if (err.isRestriction) {
                    // handle restrictions
                    console.log('Restriction error:', err);
                    this.elem.image_error.text(err);
                } else {
                    // handle other errors
                    console.error(err)
                }
            }
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return edit_tpl();
    };
}

module.exports = Edit;
