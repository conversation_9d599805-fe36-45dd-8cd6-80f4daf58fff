<div class="c-line-item" data-js="phone" data-id="{{index}}">
    <div class="f-field">
        <div class="c-l-radio">
            <input class="f-f-radio{{#if is_primary}} t-checked{{/if}}" name="primary" type="radio" value="" data-js="primary"/>
        </div>
        <label class="f-f-label">Set as Primary</label>
    </div>
    <div class="f-field">
        <label class="f-f-label">Type</label>
        <select class="f-f-input" data-js="description">
            <option>-- Select One --</option>
            {{#each descriptions}}
                <option value="{{@key}}" {{#ifeq @key ../description}} selected{{/ifeq}}>{{this}}</option>
            {{/each}}
        </select>
    </div>
    <div class="f-field">
        <label class="f-f-label">Phone #</label>
        <input class="f-f-input" type="text" value="{{number}}" data-js="number" />
    </div>
    <div class="c-li-delete">
        <a class="c-ld-button" data-js="action-delete">
            <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
            <div data-text>Delete Number</div>
        </a>
    </div>
</div>