'use strict';

// Handlebars helpers are imported globally in module.js

import Page from '@ca-package/router/src/page';
const {findChild, jsSelector} = require("@ca-package/dom");

require('@cac-js/handlebars-helpers/if_eq');
require('@cac-js/handlebars-helpers/format_currency');
require('@cac-js/handlebars-helpers/format_bid_date');
require('@cac-js/handlebars-helpers/group_by_date');
require('@cac-js/handlebars-helpers/format_date');
require('@cac-js/handlebars-helpers/format_file_size');
require('@cac-js/handlebars-helpers/format_time');
require('@cac-js/handlebars-helpers/group_by_month');

import main_tpl from '@cam-customer-portal-tpl/pages/main.hbs';
import { onClickWatcher } from '@ca-package/dom';

import { OverviewPage } from './main-pages/overview';
import { AppointmentsPage } from './main-pages/appointments';
import { BidsPage } from './main-pages/bids';
import { InvoicesPage } from './main-pages/invoices';
import { GalleryPage } from './main-pages/gallery';
import { CompanyInfoModal } from '../modals/company-info';
import { CustomerInfoModal } from '../modals/customer-info';

export class MainPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            //parent: parent,
            customer_data: window.customer_portal_data || {},
            active_menu_tab: 'overview',
            tab_items: {
                overview: {
                    title: 'Overview',
                    icon: 'system--dashboard-line',
                    is_enabled: true,
                    route: 'overview'
                },
                appointments: {
                    title: 'Appointments',
                    icon: 'business--calendar-line',
                    is_enabled: true,
                    route: 'appointments'
                },
                bids: {
                    title: 'Bids',
                    icon: 'document--file-list-2-line',
                    is_enabled: true,
                    route: 'bids'
                },
                invoices: {
                    title: 'Invoices',
                    icon: 'document--receipt-line',
                    is_enabled: true,
                    route: 'invoices'
                },
                gallery: {
                    title: 'Gallery',
                    icon: 'media--image-line',
                    is_enabled: true,
                    route: 'gallery'
                },
            }
        });
    };

    /**
     * Convert hex color to rgba
     *
     * @param {string} hex
     * @param {number} [alpha=1]
     * @returns {string}
     */
    hexToRgba(hex, alpha = 1) {
        hex = hex.replace(/^#/, '');
        if (hex.length === 3) {
            hex = hex.split('').map(x => x + x).join('');
        }
        const num = parseInt(hex, 16);
        const r = (num >> 16) & 255;
        const g = (num >> 8) & 255;
        const b = num & 255;
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    /**
     * Apply company branding to the page
     *
     */
    applyBranding() {
        const colors = this.state?.customer_data?.company_colors ?? {};
        const style = document.documentElement.style;
        // Primary
        style.setProperty('--primary-initial', colors.button_background_color);
        style.setProperty('--primary-hover', colors.hover_background_color);
        style.setProperty('--primary-text', colors.button_text_color);
        style.setProperty('--primary-text-hover', colors.hover_text_color);
        //Tab
        style.setProperty('--tab-text', colors.button_background_color);
        style.setProperty('--tab-background',this.hexToRgba(colors.button_background_color, 0.2));
        style.setProperty('--tab=background-hover', colors.button_text_color);
        style.setProperty('--tab-border', this.hexToRgba(colors.button_background_color, 0.3));
    }

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.tab_menu = findChild(root, jsSelector('tab-menu'));
        this.elem.tab_item = findChild(root, jsSelector('tab-item'));

        this.elem.loader = findChild(root, jsSelector('loader'));
        this.elem.page_container = findChild(root, jsSelector('page-container'));
        this.elem.info_button = root.find('.c-hl-info-button');
        this.elem.user_profile_pill = root.find('.c-hnup-pill');
        this.elem.user_profile_icon = root.find('.c-hnup-icon');

        for (let item of this.elem.tab_item) {
            let $item = $(item);
            this.state.tab_items[$item.data('id')]['elem'] = $item;
        }

        let that = this;

        onClickWatcher(this.elem.tab_menu, jsSelector('tab-item'), function() {
            if ($(this).hasClass('t-disabled')) {
                return false;
            }
            let id = $(this).data('id');
            that.setActiveTabMenu(id);
            return false;
        }, true);

        this.state.company_info_modal = new CompanyInfoModal();
        this.state.customer_info_modal = new CustomerInfoModal();

        if (this.elem.info_button.length > 0) {
            this.elem.info_button.on('click', function(e) {
                e.preventDefault();
                that.openCompanyInfoModal();
                return false;
            });
        }

        // Add event handlers for customer info modal
        if (this.elem.user_profile_pill.length > 0) {
            this.elem.user_profile_pill.on('click', function(e) {
                e.preventDefault();
                that.openCustomerInfoModal();
                return false;
            });
        }

        if (this.elem.user_profile_icon.length > 0) {
            this.elem.user_profile_icon.on('click', function(e) {
                e.preventDefault();
                that.openCustomerInfoModal();
                return false;
            });
        }

        this.applyBranding();
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    async load(request, next) {
        let route_id = this.router.current_route.name;
        this.setActiveItemFromRoute(route_id, route_id);
        this.router.subscribe('route-changed', this, 'onRouteChange');

        await super.load(request, next);


    };

    /**
     * Set active tab menu
     *
     * @param {string} id
     * @param {string} full_route
     */
    setActiveTabMenu(id, full_route = null) {
        if (this.state.active_menu_tab === id) {
            return;
        }
        this.state.tab_items[this.state.active_menu_tab].elem.removeClass('t-active');
        this.state.tab_items[id].elem.addClass('t-active');
        this.state.active_menu_tab = id;

        if (full_route !== null) {
            this.router.navigate(full_route);
            return;
        }
        this.router.navigate(this.state.tab_items[id].route);
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl({
            tab_items: this.state.tab_items,
            firstName: this.state.customer_data.firstName || 'Guest',
            company: this.state.customer_data.company || {},
            company_logo: this.state.customer_data.company_logo || null,
            salesperson: this.state.customer_data.salesperson || null
        });
    };

    static get routes() {
        return {
            overview: {
                default:true,
                path: '/overview',
                page: OverviewPage
            },
            appointments: {
                path: '/appointments',
                page: AppointmentsPage
            },
            bids: {
                path: '/bids',
                page: BidsPage
            },
            invoices: {
                path: '/invoices',
                page: InvoicesPage
            },
            gallery: {
                path: '/gallery',
                page: GalleryPage
            }
        }
    }

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Set active item from route id
     *
     * @param {string} route_id
     * @param {string} full_route
     */
    setActiveItemFromRoute(route_id, full_route) {
        for (let id of Object.keys(MainPage.routes)) {
            if (route_id.indexOf(id) !== 0) {
                continue;
            }
            this.setActiveTabMenu(id, full_route);
            break;
        }
    };

    /**
     * Handle a route change
     *
     * Automatically updates the selected tab based on the new routes id
     *
     * @param {object} current
     * @param {object} previous
     */
    onRouteChange({current}) {
        this.setActiveTabMenu(current.name);
    };

    /**
     * Open company information modal
     */
    openCompanyInfoModal() {
        this.state.company_info_modal.open({
            company: this.state.customer_data.company || {},
            salesperson: this.state.customer_data.salesperson || null
        });
    };

    /**
     * Open customer information modal
     */
    openCustomerInfoModal() {
        this.state.customer_info_modal.open({
            customer: {
                business_name: this.state.customer_data.business_name || this.state.customer_data.company?.name || null,
                name: this.state.customer_data.firstName && this.state.customer_data.lastName
                    ? `${this.state.customer_data.firstName} ${this.state.customer_data.lastName}`
                    : this.state.customer_data.name || null,
                phone: this.state.customer_data.phone || null,
                email: this.state.customer_data.email || null
            }
        });
    };

    /**
     * Unload component
     *
     * @param {object} request
     * @param next
     */
    async unload(request, next) {
        this.router.unsubscribe('route-changed', this);
        $(window).off('resize');
        await super.unload(request, next);
    };
}
