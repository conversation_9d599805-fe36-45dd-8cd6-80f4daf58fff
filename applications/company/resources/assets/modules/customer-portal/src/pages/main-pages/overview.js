'use strict';

import Page from '@ca-package/router/src/page';
import overview_tpl from '@cam-customer-portal-tpl/pages/main-pages/overview.hbs';
import {Client} from '../../lib/client';
import {createErrorMessage} from '@cas-notification-toast-js/message/error';
import {lightboxGallery} from '../../lib/lightbox-gallery';
const {findChild, jsSelector} = require("@ca-package/dom");

require('@cac-js/handlebars-helpers/if_eq');
require('@cac-js/handlebars-helpers/format_currency');
require('@cac-js/handlebars-helpers/format_bid_date');
require('@cac-js/handlebars-helpers/group_by_date');
require('@cac-js/handlebars-helpers/format_date');
require('@cac-js/handlebars-helpers/format_file_size');
require('@cac-js/handlebars-helpers/format_time');
require('@cac-js/handlebars-helpers/group_by_date');
require('@cac-js/handlebars-helpers/group_by_month');

/**
 * Overview page for customer portal
 *
 */
export class OverviewPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            customer_data: window.customer_portal_data || {},
            data: {},
                loading: true,
                loaded: false,
                error: null
        });

        this.client = new Client(this.state.customer_data?.customerUUID);
    }

    async boot(root) {
        super.boot(root);
        this.setupImageCardHandlers();
        this.setupSeeAllButtons();
        this.setupMobileCarousel();
    }

    /**
     * Setup click handlers for image cards
     */
    setupImageCardHandlers() {
        if (this.elem && this.elem.root) {
            const uploads = this.state.data?.uploads || [];
            lightboxGallery.setupImageCardHandlers(
                this.elem.root,
                uploads,
                '.m-image-card',
                'data-file-id'
            );
        }
    }

    /**
     * Setup mobile carousel functionality
     */
    setupMobileCarousel() {
        if (this.elem && this.elem.root) {
            this.elem.mobileCarousel = findChild(this.elem.root, jsSelector('mobile-carousel'));
            this.elem.carouselContainer = findChild(this.elem.root, jsSelector('carousel-container'));
            this.elem.carouselNavigation = findChild(this.elem.root, jsSelector('carousel-navigation'));

            if (this.elem.mobileCarousel && this.elem.mobileCarousel.length > 0) {
                this.currentSlide = 0;
                this.totalSlides = 4;

                // Setup navigation dots
                if (this.elem.carouselNavigation && this.elem.carouselNavigation.length > 0) {
                    const dots = this.elem.carouselNavigation[0].querySelectorAll('.c-mcn-dot');
                    dots.forEach((dot, index) => {
                        $(dot).on('click', () => {
                            this.goToSlide(index);
                        });
                    });
                }

                // Setup touch/swipe gestures
                this.setupCarouselGestures();
            }
        }
    }

    /**
     * Setup touch gestures for carousel
     */
    setupCarouselGestures() {
        if (this.elem.carouselContainer && this.elem.carouselContainer.length > 0) {
            const container = this.elem.carouselContainer[0];
            let startX = 0;
            let currentX = 0;
            let isDragging = false;

            container.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                isDragging = true;
            });

            container.addEventListener('touchmove', (e) => {
                if (!isDragging) return;
                currentX = e.touches[0].clientX;
            });

            container.addEventListener('touchend', () => {
                if (!isDragging) return;
                isDragging = false;

                const diffX = startX - currentX;
                const threshold = 50; // Minimum swipe distance

                if (Math.abs(diffX) > threshold) {
                    if (diffX > 0 && this.currentSlide < this.totalSlides - 1) {
                        // Swipe left - next slide
                        this.goToSlide(this.currentSlide + 1);
                    } else if (diffX < 0 && this.currentSlide > 0) {
                        // Swipe right - previous slide
                        this.goToSlide(this.currentSlide - 1);
                    }
                }
            });
        }
    }

    /**
     * Navigate to specific slide
     * @param {number} slideIndex
     */
    goToSlide(slideIndex) {
        if (slideIndex < 0 || slideIndex >= this.totalSlides) return;

        this.currentSlide = slideIndex;

        // Update carousel position
        if (this.elem.carouselContainer && this.elem.carouselContainer.length > 0) {
            const container = this.elem.carouselContainer[0];
            const translateX = -slideIndex * 25; // 25% per slide
            container.style.transform = `translateX(${translateX}%)`;
        }

        // Update navigation dots
        if (this.elem.carouselNavigation && this.elem.carouselNavigation.length > 0) {
            const dots = this.elem.carouselNavigation[0].querySelectorAll('.c-mcn-dot');
            dots.forEach((dot, index) => {
                if (index === slideIndex) {
                    dot.classList.add('t-active');
                } else {
                    dot.classList.remove('t-active');
                }
            });
        }
    }

    /**
     * Setup "See All" buttons
     */
    setupSeeAllButtons() {
        if (this.elem && this.elem.root) {
            this.elem.seeAllAppointments = findChild(this.elem.root, jsSelector('see-all-appointments'));
            this.elem.seeAllBids = findChild(this.elem.root, jsSelector('see-all-bids'));
            this.elem.seeAllInvoices = findChild(this.elem.root, jsSelector('see-all-invoices'));
            this.elem.seeAllGallery = findChild(this.elem.root, jsSelector('see-all-gallery'));

            if (this.elem.seeAllAppointments && this.elem.seeAllAppointments.length > 0) {
                for (let i = 0; i < this.elem.seeAllAppointments.length; i++) {
                    $(this.elem.seeAllAppointments[i]).on('click', (event) => {
                        event.preventDefault();
                        this.router.navigate('appointments');
                    });
                }
            }

            if (this.elem.seeAllBids && this.elem.seeAllBids.length > 0) {
                for (let i = 0; i < this.elem.seeAllBids.length; i++) {
                    $(this.elem.seeAllBids[i]).on('click', (event) => {
                        event.preventDefault();
                        this.router.navigate('bids');
                    });
                }
            }

            if (this.elem.seeAllInvoices && this.elem.seeAllInvoices.length > 0) {
                for (let i = 0; i < this.elem.seeAllInvoices.length; i++) {
                    $(this.elem.seeAllInvoices[i]).on('click', (event) => {
                        event.preventDefault();
                        this.router.navigate('invoices');
                    });
                }
            }

            if (this.elem.seeAllGallery && this.elem.seeAllGallery.length > 0) {
                for (let i = 0; i < this.elem.seeAllGallery.length; i++) {
                    $(this.elem.seeAllGallery[i]).on('click', (event) => {
                        event.preventDefault();
                        this.router.navigate('gallery');
                    });
                }
            }
        }
    }

    /**
     * Fetch all data from API endpoints
     *
     * @returns {Promise<void>}
     */
    async fetchAllData() {
        try {
            this.state.loading = true;
            this.state.error = null;

            // Fetch all data concurrently for better performance
            const [appointments, bids, invoices, uploads] = await Promise.all([
                this.client.fetchData('appointments'),
                this.client.fetchData('bids'),
                this.client.fetchData('invoices'),
                this.client.fetchData('uploads')
            ]);

            // Update state with fetched data
            Object.assign(this.state.data, {
                appointments,
                bids,
                invoices,
                uploads
            });

            this.state.loaded = true;
        } catch (error) {
            this.state.error = error;
            const message = createErrorMessage('Unable to load overview data. Please refresh the page and try again.');
            this.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Update the page content after data changes
     */
    updateContent() {
        if (this.elem && this.elem.root) {
            this.elem.root.html(this.render());
            this.setupImageCardHandlers();
            this.setupSeeAllButtons();
            this.setupMobileCarousel();
        }
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.parent && typeof this.state.parent.showLoader === 'function') {
            this.state.parent.showLoader();
        }

        try {
            await this.fetchAllData();
            this.updateContent();
        } finally {
            // Hide loader
            if (this.state.parent && typeof this.state.parent.hideLoader === 'function') {
                this.state.parent.hideLoader();
            }
        }

        await super.load(request, next);
    }

    /**
     * Unload page and cleanup
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        if (this.elem && this.elem.root) {
            lightboxGallery.removeImageCardHandlers(this.elem.root, '.m-image-card');
        }

        await super.unload(request, next);
    }

    /**
     * Refresh page data
     *
     * @param {object} request
     */
    async refresh(request) {
        this.state.data = {};
        this.state.loaded = false;
        this.state.error = null;
        await this.fetchAllData();
        this.updateContent();
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return overview_tpl({
            customer_data: this.state.customer_data,
            appointments: this.state.data.appointments,
            bids: this.state.data.bids,
            invoices: this.state.data.invoices,
            uploads: this.state.data.uploads,
            loading: this.state.loading,
            loaded: this.state.loaded,
            error: this.state.error
        });
    }
}
