'use strict';

// Import handlebars helpers BEFORE templates
require('@cac-js/handlebars-helpers/if_eq');
require('@cac-js/handlebars-helpers/format_currency');
require('@cac-js/handlebars-helpers/format_bid_date');
require('@cac-js/handlebars-helpers/group_by_date');
require('@cac-js/handlebars-helpers/format_date');
require('@cac-js/handlebars-helpers/format_file_size');
require('@cac-js/handlebars-helpers/format_time');
require('@cac-js/handlebars-helpers/group_by_month');

import Page from '@ca-package/router/src/page';
import invoices_tpl from '@cam-customer-portal-tpl/pages/main-pages/invoices.hbs';
import {Client} from '../../lib/client';
import { createErrorMessage } from '@cas-notification-toast-js/message/error'
import { DateSort } from '../../lib/date-sort';

/**
 * Invoices page for customer portal
 *
 * @memberof module:CustomerPortal/Pages/MainPages
 */
export class InvoicesPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            customer_data: window.customer_portal_data || {},
            invoices: [],
            loading: true,
            loaded: false,
            error: null
        });

        this.client = new Client(this.state.customer_data?.customerUUID);
        this.dateSort = null;
    }

    /**
     * Fetch data from an API endpoint
     *
     * @returns {Promise<void>}
     */
    async fetchData() {
        try {
            this.state.loading = true;
            this.state.error = null;

            const invoices = await this.client.fetchData('invoices');

            Object.assign(this.state, {
                invoices
            });

            this.state.loaded = true;
            console.log('Data loaded successfully:', this.state.data);
        } catch (error) {
            console.error('Error fetching overview data:', error);
            this.state.error = error;
            const message = createErrorMessage('Unable to load overview data. Please refresh the page and try again.');
            this.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.parent && typeof this.state.parent.showLoader === 'function') {
            this.state.parent.showLoader();
        }

        try {
            await this.fetchData();
            this.updateContent();
        } finally {
            // Hide loader
            if (this.state.parent && typeof this.state.parent.hideLoader === 'function') {
                this.state.parent.hideLoader();
            }
        }

        await super.load(request, next);
    }

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    async boot(root) {
        super.boot(root);
        this.setupDateSort();
    }

    /**
     * Setup date sorting functionality
     */
    setupDateSort() {
        if (this.elem && this.elem.root) {
            this.dateSort = new DateSort(this.elem.root, () => {
                this.updateContent();
            });
        }
    }

    /**
     * Update the page content after data changes
     */
    updateContent() {
        if (this.elem && this.elem.root) {
            this.elem.root.html(this.render());
            this.setupDateSort();
        }
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        // Sort invoices based on current sort order
        let sortedInvoices = this.state.invoices;
        if (this.dateSort) {
            sortedInvoices = this.dateSort.sortInvoices(this.state.invoices);
        }

        return invoices_tpl({
            customer_data: this.state.customer_data,
            invoices: sortedInvoices
        });
    }
}