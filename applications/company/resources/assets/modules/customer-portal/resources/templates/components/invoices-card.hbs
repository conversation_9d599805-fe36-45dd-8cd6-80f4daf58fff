<!-- Invoices card component -->
<div class="c-card">
    <header class="c-card-header">
        <h3>{{#if title}}{{title}}{{else}}Open Invoices{{/if}}</h3>
        <div class="c-ch-actions">
            {{#unless showSeeAll}}
                {{> partials/date-sort-toggle}}
            {{/unless}}
            {{#if showSeeAll}}
                <a class="c-see-all" href="#" data-js="see-all-invoices">See All →</a>
            {{/if}}
        </div>
    </header>
    {{#if invoices.length}}
        <div class="c-invoices-container">
            <div class="c-ic-grid">
                {{#each invoices}}
                    <div class="m-content-box m-invoice-card">
                        <div class="c-ic-title">
                            {{#if
                                bidAcceptanceName
                            }}{{bidAcceptanceName}}{{else if
                                projectCompleteName
                            }}{{projectCompleteName}}{{else}}Invoice{{/if}}
                        </div>
                        <div class="c-ic-subtitle">
                            Due
                            {{#if bidAccepted}}{{formatBidDate
                                    bidAccepted
                                }}{{/if}},
                            <span class="c-ics-note">(At Bid Acceptance)</span>
                        </div>
                        <div class="c-ic-rows">
                            <div class="c-ic-row">
                                <span class="c-icr-label">Invoice Total</span>
                                <span
                                    class="c-icr-divider"
                                    aria-hidden="true"
                                ></span>
                                <span class="c-icr-amount">
                                    {{#if
                                        bidAcceptanceAmount
                                    }}{{formatCurrency
                                            bidAcceptanceAmount
                                        }}{{else if
                                        projectCompleteAmount
                                    }}{{formatCurrency
                                            projectCompleteAmount
                                        }}{{else}}$0.00{{/if}}
                                </span>
                            </div>
                            <div class="c-ic-row">
                                <span class="c-icr-label">Remaining
                                    Balance</span>
                                <span
                                    class="c-icr-divider"
                                    aria-hidden="true"
                                ></span>
                                <span class="c-icr-amount">
                                    {{#if
                                        remainingBalance
                                    }}{{formatCurrency
                                            remainingBalance
                                        }}{{/if}}
                                </span>
                            </div>
                        </div>
                        <div class="c-ic-footer">
                            <div class="c-icf-action">
                                <button class="c-icfa-btn">
                                    <span>Invoice</span>
                                    <svg class="c-icfab-icon"><use
                                            xlink:href="#remix-icon--system--download-line"
                                        ></use></svg>
                                </button>
                            </div>
                            <div class="c-icf-date">
                                Created
                                {{#if bidFirstSent}}{{formatBidDate
                                        bidFirstSent
                                    }}{{/if}}
                            </div>
                        </div>
                    </div>
                {{/each}}
            </div>
        </div>
    {{else}}
        <div class="c-empty">
            <svg class="c-sbciw-image"><use
                    xlink:href="#module--customer-portal--invoices"
                ></use></svg>
            <p class="title">No Invoices</p>
            <p class="subtitle">
                Your invoice inbox is as empty as a desert! Nothing
                to pay.
            </p>
        </div>
    {{/if}}
</div>
