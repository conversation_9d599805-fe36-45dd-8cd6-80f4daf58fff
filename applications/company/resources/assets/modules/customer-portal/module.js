'use strict';

import './resources/sass/main.scss';
import {find, jsSelector} from '@ca-package/dom';

const $ = require('jquery');
window.$ = window.jQuery = $;

require('@fancyapps/fancybox');
require('@ca-package/dom/src/jquery_plugin');
require('remixicon/icons/System/dashboard-line.svg');
require('remixicon/icons/Business/calendar-line.svg');
require('remixicon/icons/Document/file-list-2-line.svg');
require('remixicon/icons/Document/receipt-line.svg');
require('remixicon/icons/Media/image-line.svg');
require('remixicon/icons/User & Faces/user-settings-line.svg');
require('remixicon/icons/Map/direction-line.svg');
require('remixicon/icons/Device/phone-line.svg');
require('remixicon/icons/Business/mail-add-line.svg');
require('remixicon/icons/System/share-circle-line.svg');
require('remixicon/icons/System/time-line.svg');
require('remixicon/icons/User & Faces/account-circle-line.svg');
require('remixicon/icons/System/download-line.svg');
require('remixicon/icons/Document/file-line.svg');
require('remixicon/icons/System/eye-line.svg');
require('remixicon/icons/System/information-line.svg');
require('remixicon/icons/System/close-line.svg');
require('remixicon/icons/Arrows/arrow-down-line.svg');
require('remixicon/icons/Arrows/arrow-up-line.svg');
// Handlebars helpers are imported in individual page files as needed

const svgs = require.context('./resources/svg-symbols', true, /\.svg$/);
svgs.keys().forEach(svgs);

import {Controller} from './src/index';
window.CustomerPortal = new Controller(find(jsSelector('customer-portal-root')));
