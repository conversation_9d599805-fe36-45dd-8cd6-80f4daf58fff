'use strict';

const Handlebars = require('handlebars/runtime');

/**
 * Get ordinal suffix for a number
 * @param {number} num - Number to get suffix for
 * @returns {string} Ordinal suffix
 */
function getOrdinalSuffix(num) {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
}

/**
 * Group appointments by month helper for Handlebars
 * @param {Array} appointments - Array of appointment objects
 * @param {string} sortOrder - Sort order ('newest' or 'oldest')
 * @returns {Array} Array of month groups with appointments
 */
function groupByMonth(appointments, sortOrder = 'newest') {
    if (!appointments || !Array.isArray(appointments)) return [];

    const groups = {};

    appointments.forEach((appointment) => {
        if (!appointment || !appointment.scheduledStart) return;

        try {
            const date = new Date(appointment.scheduledStart);

            // Check if date is valid
            if (isNaN(date.getTime())) {
                console.warn('Invalid date for appointment:', appointment);
                return;
            }

            const monthKey = date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long'
            });

            if (!groups[monthKey]) {
                groups[monthKey] = {
                    month: monthKey,
                    appointments: [],
                    sortDate: new Date(date.getFullYear(), date.getMonth(), 1) // First day of month for sorting
                };
            }

            groups[monthKey].appointments.push(appointment);
        } catch (error) {
            console.warn('Error processing appointment date:', appointment, error);
        }
    });

    return Object.values(groups).sort((a, b) => {
        return sortOrder === 'newest' ? b.sortDate - a.sortDate : a.sortDate - b.sortDate;
    });
}

Handlebars.registerHelper('groupByMonth', groupByMonth);
